import { isEqual } from 'lodash';
import React, { FC } from 'react';
import { RouteComponentProps } from 'react-router';
import { Route, Switch } from 'react-router-dom';

import { Active } from '../../../../model/Statuses';
import { IDropdownAction } from '../../controls/Dropdown';
import { IFilterBarComponentProps } from '../../controls/FilterBar/FilterBar';
import {
  DefaultPageSize,
  DefaultPageSizeForCards,
} from '../../other/PageSizeSelect';
import { ITranslationContextProps } from '../../utils/Translations';
import { SimpleCrudFormCreate, SimpleCrudFormUpdate } from './SimpleCrudForm';
import SimpleCrudList from './SimpleCrudList';
import { isTable, CARDS_LIST } from './countSelectorType';
import { ICustomizeColumnProps } from '../../controls/FilterBar/CustomizeColumn';
import { IContentViewSelectorProps } from '../../controls/FilterBar/ContentViewSelector';

export default class SimpleCrudBase<
  TEntity = {},
  TForm = {},
  P extends TSimpleCrudBaseProps<TEntity, TForm> = TSimpleCrudBaseProps<
    TEntity,
    TForm
  >,
  S extends TSimpleCrudBaseState = TSimpleCrudBaseState
> extends React.PureComponent<P, S> {
  static defaultProps = {
    list: {},
    hasFilters: true,
    hasPagination: true,
    hasPaginationRouting: false,
    countSelectorType: CARDS_LIST,
  };

  constructor(props) {
    super(props);

    this.state = {
      initialFilter: {},
      filter: {
        count: isTable(props.countSelectorType)
          ? DefaultPageSize
          : DefaultPageSizeForCards,
        first: 0,
        status: Active.value,
        searchQuery: '',
      },
      hasReceivedFilterFromStore: false,
    } as Readonly<S>;
  }

  static getDerivedStateFromProps({ list }, prevState) {
    const { initialFilter } = list;
    if (!isEqual(initialFilter, prevState.initialFilter)) {
      return {
        initialFilter,
        filter: {
          ...prevState.filter,
          ...initialFilter,
        },
      };
    }

    return null;
  }

  get listRoute() {
    const {
      list: { route },
    } = this.props;
    return route ? `${this.url}/${route}` : this.url;
  }

  get formRoute() {
    const { form } = this.props;

    if (!form) {
      return '';
    }
    return form.route ? `${this.url}/${form.route}` : this.url;
  }

  get addRoute() {
    return `${this.formRoute}/add`;
  }

  get updateRoute() {
    return `${this.formRoute}/edit`;
  }

  get url() {
    const { match } = this.props;
    return match ? match.url : '';
  }

  goToCreate = () => {
    const {
      history,
      form,
      list: { goToCreate },
    } = this.props;

    if (!form) {
      return;
    }

    if (form.skip) {
      return;
    }

    if (goToCreate) {
      return goToCreate(this.addRoute);
    }

    history.push(this.addRoute);
  };

  goToUpdate = (model, payload) => {
    const { id } = model;
    const {
      history,
      form,
      list: { goToUpdate },
    } = this.props;

    if (!form) {
      return;
    }

    if (form.skip) {
      return;
    }

    const url = `${this.updateRoute}/${id}`;

    if (goToUpdate) {
      return goToUpdate(model, { payload, url });
    }

    history.push(url);
  };

  goToList = () => {
    const { history } = this.props;
    history.push(this.listRoute);
  };

  handleChangeFilter = filter => {
    const {
      list: { onFilterChange, filterKey },
    } = this.props;

    // Check if this is the first filter change and we have a filterKey
    // This indicates the filter store has loaded saved values
    if (filterKey && !this.state.hasReceivedFilterFromStore) {
      this.setState({
        filter,
        hasReceivedFilterFromStore: true
      });
    } else {
      this.setState({ filter });
    }

    onFilterChange && onFilterChange(filter || {});
  };

  renderCreate = () => {
    const { form, title, list } = this.props;

    return (
      <SimpleCrudFormCreate
        {...form}
        goToBack={this.goToList}
        goToUpdate={this.goToUpdate}
        items={list.items}
        title={title}
      />
    );
  };

  renderUpdate = () => {
    const { form, title, list, match } = this.props;

    return (
      <SimpleCrudFormUpdate
        {...form}
        goToBack={this.goToList}
        id={Number(match ? match.params.id : null)}
        items={list.items}
        title={title}
      />
    );
  };

  renderList = () => {
    const { list, title } = this.props;
    const { filter } = this.state;

    return (
      <SimpleCrudList
        {...list}
        filter={filter}
        goToCreate={this.goToCreate}
        goToUpdate={this.goToUpdate}
        title={title}
        onChangeFilter={this.handleChangeFilter}
      />
    );
  };

  render() {
    const { form } = this.props;

    if (!form) {
      return (
        <Switch>
          <Route path={this.url}>{this.renderList()}</Route>
        </Switch>
      );
    }

    return (
      <Switch>
        {form.skip ? null : (
          <Route path={this.addRoute}>{this.renderCreate()}</Route>
        )}
        {form.skip ? null : (
          <Route path={`${this.updateRoute}/:id`}>{this.renderUpdate()}</Route>
        )}
        <Route path={this.url}>{this.renderList()}</Route>
      </Switch>
    );
  }
}

export type TSimpleCrudBaseProps<TEntity = {}, TForm = {}> = {
  title: string | ((entity: TEntity) => string);
  renderTitle?: (title: string) => JSX.Element;
  panelClassName?: string;
  isLoading?: boolean;
  list: {
    searchProps?: object;
    card?: {
      body?: (entity: TEntity) => JSX.Element | null;
      backgroundImage?: string;
      title?: (model: TEntity) => string;
      size?: string;
      leftLabel?: string | ((node: TEntity) => string | JSX.Element);
      middleLabel?: string | ((node: TEntity) => string | JSX.Element);
      className?: string;
      addExtraActions?: (
        isEditSessionActive: boolean,
        node: TEntity,
        onCreate: () => void,
      ) => IDropdownAction[];
      hasDeleteButton?: ((model: TEntity) => boolean) | boolean;
      hasDeleteConfirmationModal?: boolean;
      deleteMessage?: string;
    };
    wrapper?: ({
      children,
    }: {
      children: JSX.Element;
      goToCreate: () => void;
      title: string;
    }) => JSX.Element;
    route?: string;
    title?: string;
    customTitle?: string;
    items?: object[];
    filterComponent?: {
      [key: string]:
        | React.ComponentType<IFilterBarComponentProps<any>>
        | React.FC<ICustomizeColumnProps>
        | React.FC<IContentViewSelectorProps>;
    };
    filterComponentProps?: {
      [key: string]: object;
    };
    initialFilter?: object;
    hasCollapsableFilters?: boolean;
    collapseButtonAlign?: 'right' | 'left';
    onFilterChange?: (filter: object) => void;
    onFilterChanged?: (filter: object, name: string) => void;
    hasSearchFieldOnly?: boolean;
    rightActionButtons?: ((props: any) => JSX.Element) | JSX.Element | null;
    hasClearStyles?: boolean;
    panelClassName?: string;
    hasGridPagination?: boolean;
    isAllPageOptions?: boolean;
    linkNewTab?: boolean;
    deleteBodyText?: string;
    renderTitle?: (title: string) => JSX.Element | string;
    filterKey?: string;
    hasFormActions?: boolean;
    onRowClick?: (row: TEntity, payload?: any) => void;
    onBeforeFilterUpdate?: (values: any) => any;
    onBeforeFilterComplete?: (values: any) => any;
    goToUpdate?: (
      entity: TEntity,
      params: { payload?: object; url: string },
    ) => void;
    goToCreate?: (url: string) => void;
  };

  form?: {
    component:
      | FC<TSimpleCrudFormProps<TEntity, TForm>>
      | React.Component<TSimpleCrudFormProps<TEntity, TForm>>;
    default?: Partial<TEntity>;
    route?: string;
    props?: TForm;
    hasPanel?: boolean;
    wrapper?: (element: JSX.Element) => JSX.Element;
    skip?: boolean;
    title?: string | ((object: TEntity) => string);
    rightActionButtons?: ((props: any) => JSX.Element) | JSX.Element | null;
    hasClearStyles?: boolean;
    linkNewTab?: boolean;
    cookItemId?: (item: TEntity) => string;
  };
  hasFilters?: boolean;
  hasPagination?: boolean;
  hasPaginationRouting?: boolean;
  renderMode?: TGqlFullCrudRenderMode;
  onRowClick?: (row: TEntity) => void;
} & RouteComponentProps<{ id: string }> &
  ITranslationContextProps;

export type TSimpleCrudBaseState = {
  initialFilter: object;
  filter: {
    count: number;
    first: number;
    status: string;
    searchQuery: string;
  };
  hasReceivedFilterFromStore: boolean;
};

export interface ISimpleCrudFormPropsBase<TEntity> {
  entity: TEntity;
  error?: object | undefined;
  goToPreview?: boolean | undefined;
  onGoBack: () => void;
  onDelete?: () => void;
  onCancel: (() => void) | undefined;
  onSubmit: ((entity: TEntity) => Promise<void>) | ((entity: TEntity) => void);
}

export type TSimpleCrudFormProps<
  TEntity = {},
  TForm = {}
> = ISimpleCrudFormPropsBase<TEntity> & TForm;

export const RENDER_MODE = {
  COMMON: 'common' as TGqlFullCrudRenderMode,
  TREE: 'tree' as TGqlFullCrudRenderMode,
  TABBED: 'tabbed' as TGqlFullCrudRenderMode,
};

export type TGqlFullCrudRenderMode = 'common' | 'tree' | 'tabbed';
